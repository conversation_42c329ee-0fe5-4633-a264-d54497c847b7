import React, { useState, useEffect } from 'react';
import { useTopicListGeneration, useTopicContentGeneration } from '../../hooks/useLLMGeneration';
import { Subject } from '../../hooks/useSubjects';
import { Topic } from '../../hooks/useTopics';
import { useTopics } from '../../hooks/useTopics';
import { LLMProvider } from '../../services/llmAdapter';
import LLMProviderSelector from './LLMProviderSelector';


interface TopicGeneratorFormProps {
  subjects: Subject[];
  onSubjectChange: (subjectId: string | null) => void;
}

/**
 * Form for generating and saving topics using LLM
 */
const TopicGeneratorForm: React.FC<TopicGeneratorFormProps> = ({ subjects, onSubjectChange }) => {
  const [selectedSubject, setSelectedSubject] = useState<Subject | null>(null);
  const [generatedTopics, setGeneratedTopics] = useState<Partial<Topic>[]>([]);
  const [selectedTopicTitle, setSelectedTopicTitle] = useState<string | null>(null);
  const [generatedContent, setGeneratedContent] = useState<Partial<Topic> | null>(null);
  const [saveSuccess, setSaveSuccess] = useState(false);

  const [gradeLevel, setGradeLevel] = useState<string>('');

  // LLM Provider state
  const [selectedProvider, setSelectedProvider] = useState<LLMProvider>(LLMProvider.OPENAI);
  const [selectedModel, setSelectedModel] = useState<string>('gpt-4o');
  
  const { generateTopicList, loading, error } = useTopicListGeneration();
  const { topics, saveTopics, saveSingleTopic, isSaving, saveError } = useTopics(selectedSubject?.id || null);
  const { generateTopicContent, loading: contentLoading, error: contentError } = useTopicContentGeneration();

  // Update selected subject when subjects load
  useEffect(() => {
    console.log('Subjects changed:', subjects);
    console.log('Current selectedSubject:', selectedSubject);
    
    if (subjects.length > 0 && !selectedSubject) {
      console.log('Setting initial subject:', subjects[0]);
      setSelectedSubject(subjects[0]);
      onSubjectChange(subjects[0].id);
    } else if (subjects.length === 0) {
      // Reset selected subject if no subjects are available
      console.log('No subjects available, resetting selection');
      setSelectedSubject(null);
      onSubjectChange(null);
    }
  }, [subjects, selectedSubject, onSubjectChange]);

  // Update grade level when subject changes
  useEffect(() => {
    if (selectedSubject && selectedSubject.grade_levels && selectedSubject.grade_levels.length > 0) {
      setGradeLevel(selectedSubject.grade_levels.join('-'));
    } else {
      setGradeLevel(''); // Reset if subject has no grade levels defined
    }
  }, [selectedSubject]);



  // Handle subject change
  const handleSubjectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const subjectId = e.target.value;
    console.log('Subject selected from dropdown:', subjectId);
    const subject = subjects.find(s => s.id === subjectId) || null;
    console.log('Found subject object:', subject);
    setSelectedSubject(subject);
    onSubjectChange(subjectId);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedSubject) return;

    const result = await generateTopicList(selectedSubject.name, gradeLevel, selectedProvider, selectedModel);
    if (result) {
      setGeneratedTopics(result);
    }
  };

  const handleTopicSelect = async (topicTitle: string) => {
    setSelectedTopicTitle(topicTitle);
    if (!selectedSubject) return;

    const content = await generateTopicContent(
      selectedSubject.name,
      topicTitle,
      gradeLevel,
      selectedProvider,
      selectedModel
    );

    if (content) {
      setGeneratedContent({ ...content, title: topicTitle });
    }
  };

  const handleSaveTopics = async () => {
    if (!selectedSubject || generatedTopics.length === 0) return;
    setSaveSuccess(false);

    // Convert generated topics to proper Topic objects
    const topicsToSave = generatedTopics.map(topic => ({
      title: topic.title!,
      description: topic.description || null,
      content: topic.content || null,
      difficulty_level: topic.difficulty_level || 1,
      estimated_study_time_minutes: topic.estimated_study_time_minutes || 30,
      learning_objectives: topic.learning_objectives || null,
      display_order: 0,
      is_published: true,
    }));

    const success = await saveTopics(selectedSubject.id, topicsToSave);
    if (success) {
      setSaveSuccess(true);
    }
  };

  const handleSaveSingleTopic = async () => {
    if (!generatedContent || !selectedSubject) return;
    setSaveSuccess(false);

    const topicToSave: Partial<Topic> = {
      ...generatedContent,
      subject_id: selectedSubject.id,
      is_published: false, // Default to not published
    };

    const success = await saveSingleTopic(topicToSave);
    if (success) {
      setSaveSuccess(true);
      setGeneratedContent(null); // Clear the form on success
      setSelectedTopicTitle(null);
    }
  };

  // Reset the form
  const handleReset = () => {
    setGeneratedTopics([]);
    setSaveSuccess(false);
  };

  const existingTopicTitles = new Set(topics.map(t => t.title));

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">Generate New Topic List</h2>

      {/* Input form */}
      <form onSubmit={handleSubmit} className="mb-6">
        {/* LLM Provider Selection */}
        <div className="mb-6">
          <LLMProviderSelector
            selectedProvider={selectedProvider}
            selectedModel={selectedModel}
            onProviderChange={setSelectedProvider}
            onModelChange={setSelectedModel}
            disabled={loading}
          />
        </div>

        <div className="mb-4">
          <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-1">
            Select Subject:
          </label>
          {subjects.length > 0 ? (
            <select
              id="subject"
              value={selectedSubject?.id || ''}
              onChange={handleSubjectChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              disabled={loading}
            >
              {subjects.map(subject => (
                <option key={subject.id} value={subject.id}>
                  {subject.name}
                </option>
              ))}
            </select>
          ) : (
            <div className="p-3 bg-yellow-100 border border-yellow-400 text-yellow-700 rounded">
              No subjects available. Please create a subject first before generating topics.
            </div>
          )}
        </div>

        <div className="mb-4">
          <label htmlFor="gradeLevel" className="block text-sm font-medium text-gray-700 mb-1">
            Grade Levels (e.g., 9-10)
          </label>
          <input
            id="gradeLevel"
            type="text"
            value={gradeLevel}
            onChange={(e) => setGradeLevel(e.target.value)}
            placeholder="Optional: e.g., 9-10"
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            disabled={loading}
          />
        </div>
        
        <div className="flex gap-2">
          <button
            type="submit"
            disabled={loading || subjects.length === 0 || !selectedSubject}
            className={`px-4 py-2 rounded-md text-white ${
              loading || subjects.length === 0 || !selectedSubject
                ? 'bg-blue-300 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700'
            } transition-colors`}
          >
            {loading ? 'Loading...' : 'Generate Topic List'}
          </button>
          
          <button
            type="button"
            onClick={handleReset}
            className="px-4 py-2 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
          >
            Reset
          </button>
        </div>
      </form>

      {/* Error message */}
      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      {/* Generated topics list */}
      {generatedTopics.length > 0 && (
        <div className="mt-6 p-4 border border-gray-200 rounded-lg bg-gray-50">
          <h3 className="text-lg font-semibold mb-4">Generated Topics</h3>
          <div className="flex flex-wrap gap-2">
            {generatedTopics.map((topic, index) => {
              const isSaved = existingTopicTitles.has(topic.title || '');
              return (
                <button
                  key={index}
                  type="button"
                  onClick={() => handleTopicSelect(topic.title || '')}
                  className={`flex items-center gap-2 px-3 py-1.5 bg-white border rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors ${
                    isSaved ? 'border-green-500 text-green-700' : 'border-gray-300'
                  }`}
                >
                  {topic.title}
                  {isSaved && (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clipRule="evenodd"
                      />
                    </svg>
                  )}
                </button>
              );
            })}
          </div>
          <div className="mt-4">
            <button
              onClick={handleSaveTopics}
              disabled={isSaving}
              className={`px-4 py-2 rounded-md text-white ${
                isSaving ? 'bg-green-300 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700'
              } transition-colors`}
            >
              {isSaving ? 'Saving...' : 'Save Topics to DB'}
            </button>
          </div>
        </div>
      )}

      {/* Save status messages */}
      {saveSuccess && (
        <div className="mt-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
          Topics saved successfully!
        </div>
      )}
      {saveError && (
        <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {saveError}
        </div>
      )}

      {/* Generated Content Form */}
      {contentLoading && <p className="mt-4">Generating detailed content...</p>}
      {contentError && <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">{contentError}</div>}
      {generatedContent && (
        <div className="mt-6 p-4 border border-gray-200 rounded-lg bg-white">
          <h3 className="text-lg font-semibold mb-4">Edit Topic Details: {generatedContent.title}</h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Description:</label>
              <textarea
                value={generatedContent.description || ''}
                onChange={(e) => setGeneratedContent({ ...generatedContent, description: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Difficulty Level (1-5):</label>
                <input
                  type="number"
                  min="1"
                  max="5"
                  value={generatedContent.difficulty_level || ''}
                  onChange={(e) => setGeneratedContent({ ...generatedContent, difficulty_level: parseInt(e.target.value, 10) })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Est. Study Time (minutes):</label>
                <input
                  type="number"
                  min="0"
                  value={generatedContent.estimated_study_time_minutes || ''}
                  onChange={(e) => setGeneratedContent({ ...generatedContent, estimated_study_time_minutes: parseInt(e.target.value, 10) })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Learning Objectives (one per line):</label>
              <textarea
                value={(generatedContent.learning_objectives || []).join('\n')}
                onChange={(e) => setGeneratedContent({ ...generatedContent, learning_objectives: e.target.value.split('\n') })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                rows={4}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Content (Markdown supported):</label>
              <textarea
                value={generatedContent.content || ''}
                onChange={(e) => setGeneratedContent({ ...generatedContent, content: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                rows={15}
              />
            </div>

            <button
              onClick={handleSaveSingleTopic}
              disabled={isSaving}
              className={`px-4 py-2 rounded-md text-white ${
                isSaving ? 'bg-purple-300 cursor-not-allowed' : 'bg-purple-600 hover:bg-purple-700'
              } transition-colors`}
            >
              {isSaving ? 'Saving...' : 'Save This Topic'}
            </button>
          </div>
        </div>
      )}


    </div>
  );
};

export default TopicGeneratorForm;
