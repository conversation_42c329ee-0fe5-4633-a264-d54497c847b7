import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useSubjects } from '../hooks/useSubjects';
import { useTopics } from '../hooks/useTopics';

const TopicsPage: React.FC = () => {
  const { subjectId } = useParams<{ subjectId: string }>();
  const navigate = useNavigate();
  const { subjects } = useSubjects();
  const { topics, loading, error } = useTopics(subjectId || null);
  const [currentSubject, setCurrentSubject] = useState<any>(null);

  // Find the current subject details
  useEffect(() => {
    if (subjects && subjectId) {
      const subject = subjects.find(s => s.id === subjectId);
      setCurrentSubject(subject || null);
    }
  }, [subjects, subjectId]);

  // Handle back navigation
  const handleBack = () => {
    navigate('/subjects');
  };

  // Loading state
  if (loading) {
    return (
      <div>
        <div className="flex items-center mb-8">
          <button 
            onClick={handleBack}
            className="mr-4 p-2 rounded-full hover:bg-gray-100"
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </button>
          <h1>Loading Topics...</h1>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="bg-white p-6 rounded-lg shadow-md animate-pulse">
              <div className="h-6 bg-gray-300 rounded w-3/4 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div>
        <div className="flex items-center mb-8">
          <button 
            onClick={handleBack}
            className="mr-4 p-2 rounded-full hover:bg-gray-100"
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </button>
          <h1>Topics</h1>
        </div>
        
        <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-lg">
          <h3 className="text-lg font-semibold">Error loading topics</h3>
          <p>{error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // No subject found
  if (!currentSubject) {
    return (
      <div>
        <div className="flex items-center mb-8">
          <button 
            onClick={handleBack}
            className="mr-4 p-2 rounded-full hover:bg-gray-100"
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </button>
          <h1>Subject Not Found</h1>
        </div>
        
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 p-4 rounded-lg">
          <h3 className="text-lg font-semibold">Subject not found</h3>
          <p>The subject you're looking for doesn't exist or has been removed.</p>
          <button 
            onClick={handleBack} 
            className="mt-2 px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700"
          >
            Back to Subjects
          </button>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center mb-4">
        <button 
          onClick={handleBack}
          className="mr-4 p-2 rounded-full hover:bg-gray-100"
        >
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
        </button>
        <div>
          <h1>{currentSubject.name}</h1>
          <p className="text-neutral-600">
            Explore topics and study materials for {currentSubject.name}.
          </p>
        </div>
      </div>
      
      <div 
        className="w-full h-24 mb-6 rounded-lg flex items-center justify-center p-4"
        style={{ backgroundColor: currentSubject.color_hex }}
      >
        <h2 className="text-2xl font-bold text-white">{currentSubject.name} Topics</h2>
      </div>
      
      {topics.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {topics.map((topic) => (
            <div key={topic.id} className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow">
              <div className="flex justify-between items-start mb-2">
                <h3 className="text-lg font-semibold">{topic.title}</h3>
                <span className="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">
                  {topic.difficulty_level === 1 && 'Beginner'}
                  {topic.difficulty_level === 2 && 'Elementary'}
                  {topic.difficulty_level === 3 && 'Intermediate'}
                  {topic.difficulty_level === 4 && 'Advanced'}
                  {topic.difficulty_level === 5 && 'Expert'}
                </span>
              </div>
              
              <p className="text-neutral-600 mb-4">
                {topic.description || 'No description available.'}
              </p>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-neutral-500">
                  {topic.estimated_study_time_minutes} min study time
                </span>
                <button
                  onClick={() => navigate(`/subjects/${subjectId}/topics/${topic.id}`)}
                  className="text-primary-600 hover:text-primary-700 font-medium transition-colors"
                >
                  Study Now →
                </button>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="bg-white p-6 rounded-lg shadow-md text-center">
          <h3 className="text-lg font-semibold mb-2">No Topics Available Yet</h3>
          <p className="text-neutral-600 mb-4">
            We're currently developing content for {currentSubject.name}. Check back soon!
          </p>
          <button 
            onClick={handleBack}
            className="px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700"
          >
            Back to Subjects
          </button>
        </div>
      )}
    </div>
  );
};

export default TopicsPage;
