@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Lexend:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    @apply text-neutral-900 bg-neutral-50 antialiased;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  }

  body {
    @apply leading-relaxed;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-display font-semibold tracking-tight;
  }

  h1 {
    @apply text-3xl md:text-4xl lg:text-5xl leading-tight;
  }

  h2 {
    @apply text-2xl md:text-3xl lg:text-4xl leading-tight;
  }

  h3 {
    @apply text-xl md:text-2xl lg:text-3xl leading-snug;
  }

  h4 {
    @apply text-lg md:text-xl lg:text-2xl leading-snug;
  }

  h5 {
    @apply text-base md:text-lg lg:text-xl leading-snug;
  }

  h6 {
    @apply text-sm md:text-base lg:text-lg leading-snug;
  }

  p {
    @apply leading-relaxed;
  }

  /* Enhanced form elements */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  input[type="url"],
  input[type="search"],
  textarea,
  select {
    @apply transition-all duration-200 ease-in-out;
  }

  input[type="text"]:focus,
  input[type="email"]:focus,
  input[type="password"]:focus,
  input[type="number"]:focus,
  input[type="url"]:focus,
  input[type="search"]:focus,
  textarea:focus,
  select:focus {
    @apply ring-2 ring-primary-500 ring-opacity-50 border-primary-500;
  }
}

@layer utilities {
  /* Hide scrollbar for Chrome, Safari and Opera */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Line clamp utilities */
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Touch-friendly button sizes */
  .btn-touch {
    @apply min-h-[44px] min-w-[44px];
  }

  /* Safe area padding for mobile devices */
  .safe-area-padding {
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Enhanced button styles */
  .btn-primary {
    @apply bg-primary-600 text-white font-medium px-6 py-3 rounded-xl shadow-soft hover:bg-primary-700 hover:shadow-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-opacity-50;
  }

  .btn-secondary {
    @apply bg-white text-neutral-700 font-medium px-6 py-3 rounded-xl border border-neutral-200 shadow-soft hover:bg-neutral-50 hover:border-neutral-300 hover:shadow-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-opacity-50;
  }

  .btn-success {
    @apply bg-success-600 text-white font-medium px-6 py-3 rounded-xl shadow-soft hover:bg-success-700 hover:shadow-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-success-500 focus:ring-opacity-50;
  }

  .btn-danger {
    @apply bg-error-600 text-white font-medium px-6 py-3 rounded-xl shadow-soft hover:bg-error-700 hover:shadow-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-error-500 focus:ring-opacity-50;
  }

  /* Card styles */
  .card {
    @apply bg-white rounded-2xl shadow-soft border border-neutral-200 p-6;
  }

  .card-hover {
    @apply bg-white rounded-2xl shadow-soft border border-neutral-200 p-6 hover:shadow-medium transition-all duration-200 cursor-pointer;
  }

  /* Input styles */
  .input-field {
    @apply w-full px-4 py-3 border border-neutral-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200;
  }

  /* Text styles */
  .text-muted {
    @apply text-neutral-600;
  }

  .text-subtle {
    @apply text-neutral-500;
  }

  /* Spacing utilities */
  .section-padding {
    @apply px-4 py-8 lg:px-8 lg:py-12;
  }

  .container-padding {
    @apply px-4 lg:px-8;
  }
}